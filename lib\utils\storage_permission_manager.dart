// lib/utils/storage_permission_manager.dart
// Utility class for managing storage permissions persistently

import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart'
    as permission_handler;
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';

class StoragePermissionManager {
  static const String _storagePermissionKey = 'storage_permission_granted';
  static const String _photosPermissionKey = 'photos_permission_granted';

  static StoragePermissionManager? _instance;
  static StoragePermissionManager get instance {
    _instance ??= StoragePermissionManager._internal();
    return _instance!;
  }

  StoragePermissionManager._internal();

  bool _storagePermissionGranted = false;
  bool _photosPermissionGranted = false;

  /// Safe platform detection
  bool get _isAndroid {
    try {
      return !kIsWeb && Platform.isAndroid;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Platform detection error: $e');
      }
      return false;
    }
  }

  /// Initialize permission states from SharedPreferences
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _storagePermissionGranted = prefs.getBool(_storagePermissionKey) ?? false;
      _photosPermissionGranted = prefs.getBool(_photosPermissionKey) ?? false;

      // Verify permissions are still valid
      await _verifyPermissions();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error initializing storage permissions: $e');
      }
    }
  }

  /// Verify that stored permission states are still valid
  Future<void> _verifyPermissions() async {
    try {
      if (_isAndroid) {
        // Check storage permission
        final storageStatus =
            await permission_handler.Permission.storage.status;
        if (storageStatus != permission_handler.PermissionStatus.granted) {
          _storagePermissionGranted = false;
          await _saveStoragePermissionState();
        }

        // Check photos permission (Android 13+)
        final photosStatus = await permission_handler.Permission.photos.status;
        if (photosStatus != permission_handler.PermissionStatus.granted) {
          _photosPermissionGranted = false;
          await _savePhotosPermissionState();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error verifying permissions: $e');
      }
    }
  }

  /// Request storage permissions and save state
  Future<bool> requestStoragePermission() async {
    try {
      if (_isAndroid) {
        // Request storage permission for older Android versions
        if (!_storagePermissionGranted) {
          final storageStatus =
              await permission_handler.Permission.storage.request();
          _storagePermissionGranted =
              storageStatus == permission_handler.PermissionStatus.granted;
          await _saveStoragePermissionState();
        }

        // Request photos permission for Android 13+
        if (!_photosPermissionGranted) {
          final photosStatus =
              await permission_handler.Permission.photos.request();
          _photosPermissionGranted =
              photosStatus == permission_handler.PermissionStatus.granted;
          await _savePhotosPermissionState();
        }

        // Return true if either permission is granted
        return _storagePermissionGranted || _photosPermissionGranted;
      } else {
        // iOS doesn't need explicit storage permission for saving to gallery
        return true;
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error requesting storage permission: $e');
      }
      return false;
    }
  }

  /// Check if storage permission is granted
  Future<bool> hasStoragePermission() async {
    try {
      if (_isAndroid) {
        // Check current permission status
        final storageStatus =
            await permission_handler.Permission.storage.status;
        final photosStatus = await permission_handler.Permission.photos.status;

        final hasStorage =
            storageStatus == permission_handler.PermissionStatus.granted;
        final hasPhotos =
            photosStatus == permission_handler.PermissionStatus.granted;

        // Update stored states if they changed
        if (hasStorage != _storagePermissionGranted) {
          _storagePermissionGranted = hasStorage;
          await _saveStoragePermissionState();
        }

        if (hasPhotos != _photosPermissionGranted) {
          _photosPermissionGranted = hasPhotos;
          await _savePhotosPermissionState();
        }

        return hasStorage || hasPhotos;
      } else {
        return true; // iOS doesn't need explicit permission
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error checking storage permission: $e');
      }
      return false;
    }
  }

  /// Save storage permission state to SharedPreferences
  Future<void> _saveStoragePermissionState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_storagePermissionKey, _storagePermissionGranted);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error saving storage permission state: $e');
      }
    }
  }

  /// Save photos permission state to SharedPreferences
  Future<void> _savePhotosPermissionState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_photosPermissionKey, _photosPermissionGranted);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error saving photos permission state: $e');
      }
    }
  }

  /// Reset permission states (useful for testing or when user revokes permissions)
  Future<void> resetPermissions() async {
    try {
      _storagePermissionGranted = false;
      _photosPermissionGranted = false;

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storagePermissionKey);
      await prefs.remove(_photosPermissionKey);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error resetting permissions: $e');
      }
    }
  }

  /// Get permission status for debugging
  Map<String, bool> getPermissionStatus() {
    return {
      'storage': _storagePermissionGranted,
      'photos': _photosPermissionGranted,
    };
  }

  /// Check if we should show permission rationale
  Future<bool> shouldShowPermissionRationale() async {
    try {
      if (_isAndroid) {
        final storageRationale = await permission_handler
            .Permission.storage.shouldShowRequestRationale;
        final photosRationale = await permission_handler
            .Permission.photos.shouldShowRequestRationale;
        return storageRationale || photosRationale;
      }
      return false;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error checking permission rationale: $e');
      }
      return false;
    }
  }

  /// Open app settings for manual permission grant
  Future<void> openAppSettings() async {
    try {
      await permission_handler.openAppSettings();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error opening app settings: $e');
      }
    }
  }
}
