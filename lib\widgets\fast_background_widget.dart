// lib/widgets/fast_background_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../utils/background_manager.dart';

/// Simple, reliable background widget that directly loads images
class FastBackgroundWidget extends StatelessWidget {
  final int backgroundIndex;
  final Widget? child;
  final BoxFit fit;
  final ColorFilter? colorFilter;
  final BorderRadius? borderRadius;
  final double? width;
  final double? height;
  final VoidCallback? onTap;

  const FastBackgroundWidget({
    super.key,
    required this.backgroundIndex,
    this.child,
    this.fit = BoxFit.cover,
    this.colorFilter,
    this.borderRadius,
    this.width,
    this.height,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Get the background path directly
    final backgroundPath =
        BackgroundManager.getSafeBackgroundPath(backgroundIndex);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: borderRadius,
          image: DecorationImage(
            image: AssetImage(backgroundPath),
            fit: fit,
            colorFilter: colorFilter,
            onError: (exception, stackTrace) {
              if (kDebugMode) {
                debugPrint('Error loading background: $backgroundPath');
              }
            },
          ),
        ),
        child: child,
      ),
    );
  }
}

/// Optimized background container for cards and posts
class FastBackgroundCard extends StatelessWidget {
  final Widget child;
  final int backgroundIndex;
  final VoidCallback? onTap;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;

  const FastBackgroundCard({
    super.key,
    required this.child,
    required this.backgroundIndex,
    this.onTap,
    this.padding,
    this.borderRadius,
    this.boxShadow,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding,
        decoration: BoxDecoration(
          borderRadius: borderRadius ?? BorderRadius.circular(20),
          boxShadow: boxShadow,
        ),
        child: ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.circular(20),
          child: FastBackgroundWidget(
            backgroundIndex: backgroundIndex,
            colorFilter: ColorFilter.mode(
              Colors.black.withValues(alpha: 0.3),
              BlendMode.darken,
            ),
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Instant gradient background (no image loading)
class InstantGradientBackground extends StatelessWidget {
  final Widget? child;
  final int backgroundIndex;
  final BorderRadius? borderRadius;
  final double? width;
  final double? height;

  const InstantGradientBackground({
    super.key,
    this.child,
    required this.backgroundIndex,
    this.borderRadius,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final gradientColors =
        BackgroundManager.getGradientFallback(backgroundIndex);

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: borderRadius,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            gradientColors[0],
            gradientColors[1],
            gradientColors[0].withValues(alpha: 0.8),
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
      ),
      child: child,
    );
  }
}
