// lib/widgets/fast_background_widget.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../utils/background_manager.dart';

/// Fast-loading background widget with gradient fallbacks
/// Provides instant loading with beautiful gradients while images load
class FastBackgroundWidget extends StatefulWidget {
  final String? backgroundPath;
  final int? backgroundIndex;
  final Widget? child;
  final BoxFit fit;
  final ColorFilter? colorFilter;
  final BorderRadius? borderRadius;
  final double? width;
  final double? height;
  final bool showGradientFallback;

  const FastBackgroundWidget({
    super.key,
    this.backgroundPath,
    this.backgroundIndex,
    this.child,
    this.fit = BoxFit.cover,
    this.colorFilter,
    this.borderRadius,
    this.width,
    this.height,
    this.showGradientFallback = true,
  }) : assert(backgroundPath != null || backgroundIndex != null,
            'Either backgroundPath or backgroundIndex must be provided');

  @override
  State<FastBackgroundWidget> createState() => _FastBackgroundWidgetState();
}

class _FastBackgroundWidgetState extends State<FastBackgroundWidget> {
  bool _imageLoaded = false;
  bool _imageError = false;
  late String _actualBackgroundPath;
  late List<Color> _gradientColors;

  @override
  void initState() {
    super.initState();
    _initializeBackground();
    _preloadImage();
  }

  void _initializeBackground() {
    // Determine the actual background path
    if (widget.backgroundPath != null) {
      _actualBackgroundPath = widget.backgroundPath!;
      _gradientColors = BackgroundManager.getGradientFallbackForPath(_actualBackgroundPath);
    } else if (widget.backgroundIndex != null) {
      _actualBackgroundPath = BackgroundManager.getSafeBackgroundPath(widget.backgroundIndex!);
      _gradientColors = BackgroundManager.getGradientFallback(widget.backgroundIndex!);
    } else {
      // Fallback to first background
      _actualBackgroundPath = BackgroundManager.getAllBackgrounds().first;
      _gradientColors = BackgroundManager.getGradientFallback(0);
    }
  }

  Future<void> _preloadImage() async {
    try {
      final imageProvider = AssetImage(_actualBackgroundPath);
      await precacheImage(imageProvider, context);
      if (mounted) {
        setState(() {
          _imageLoaded = true;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Failed to load background image: $_actualBackgroundPath, error: $e');
      }
      if (mounted) {
        setState(() {
          _imageError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: widget.borderRadius,
        gradient: widget.showGradientFallback ? _buildGradient() : null,
        color: widget.showGradientFallback ? null : Colors.black,
        image: _imageLoaded && !_imageError ? _buildImageDecoration() : null,
      ),
      child: widget.child,
    );
  }

  LinearGradient _buildGradient() {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        _gradientColors[0],
        _gradientColors[1],
        _gradientColors[0].withValues(alpha: 0.8),
      ],
      stops: const [0.0, 0.6, 1.0],
    );
  }

  DecorationImage? _buildImageDecoration() {
    return DecorationImage(
      image: AssetImage(_actualBackgroundPath),
      fit: widget.fit,
      filterQuality: FilterQuality.low, // Fast loading
      colorFilter: widget.colorFilter,
      onError: (exception, stackTrace) {
        if (kDebugMode) {
          debugPrint('Error loading background image: $exception');
        }
        if (mounted) {
          setState(() {
            _imageError = true;
          });
        }
      },
    );
  }
}

/// Optimized background container for cards and posts
class FastBackgroundCard extends StatelessWidget {
  final Widget child;
  final int backgroundIndex;
  final VoidCallback? onTap;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;

  const FastBackgroundCard({
    super.key,
    required this.child,
    required this.backgroundIndex,
    this.onTap,
    this.padding,
    this.borderRadius,
    this.boxShadow,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding,
        decoration: BoxDecoration(
          borderRadius: borderRadius ?? BorderRadius.circular(20),
          boxShadow: boxShadow,
        ),
        child: ClipRRect(
          borderRadius: borderRadius ?? BorderRadius.circular(20),
          child: FastBackgroundWidget(
            backgroundIndex: backgroundIndex,
            colorFilter: ColorFilter.mode(
              Colors.black.withValues(alpha: 0.3),
              BlendMode.darken,
            ),
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Instant gradient background (no image loading)
class InstantGradientBackground extends StatelessWidget {
  final Widget? child;
  final int backgroundIndex;
  final BorderRadius? borderRadius;
  final double? width;
  final double? height;

  const InstantGradientBackground({
    super.key,
    this.child,
    required this.backgroundIndex,
    this.borderRadius,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final gradientColors = BackgroundManager.getGradientFallback(backgroundIndex);
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: borderRadius,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            gradientColors[0],
            gradientColors[1],
            gradientColors[0].withValues(alpha: 0.8),
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
      ),
      child: child,
    );
  }
}
