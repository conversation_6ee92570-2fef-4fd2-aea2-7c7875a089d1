// lib/screens/todays_shots_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:gal/gal.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:ui' as ui;
import 'dart:math' as math;

import '../utils/storage_permission_manager.dart';
import '../utils/background_manager.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';
import '../data/pickup_lines_data.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/enhanced_edit_dialog.dart';
import '../widgets/fast_background_widget.dart';
import '../models/styled_text.dart';

class TodaysShotsScreen extends StatefulWidget {
  const TodaysShotsScreen({super.key});

  @override
  State<TodaysShotsScreen> createState() => _TodaysShotsScreenState();
}

class _TodaysShotsScreenState extends State<TodaysShotsScreen>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  @override
  bool get wantKeepAlive => true;

  late final List<String> backgroundImages;

  final Map<int, ValueNotifier<int>> cardBackgroundNotifiers = {};
  final Map<int, ValueNotifier<bool>> cardLikedNotifiers = {};
  final Map<int, StyledText> styledTexts = {};
  DateTime? _lastTapTime;

  List<String> lines = [];
  List<String> languages = [];
  final ScrollController _scrollController = ScrollController();

  static const List<String> englishCategories = [
    'Bold',
    'Cute',
    'Food',
    'Cleaver',
    'Genius',
    'Dirty',
    'Flirty',
    'Hookup',
    'Romantic',
    'Funny',
    'Nerd',
  ];

  static const List<String> hindiCategories = ['Hindi'];

  @override
  void initState() {
    super.initState();
    _initializeComponents();
  }

  void _initializeComponents() {
    backgroundImages = BackgroundManager.getAllBackgrounds();
    _loadTodaysShots();

    // Optimized image preloading for instant background switching
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _preloadCriticalImages(); // Single preload after first frame for better performance
    });
  }

  String _removeQuotes(String text) {
    return text
        .replaceAll(RegExp(r'^[""]'), '')
        .replaceAll(RegExp(r'[""]$'), '')
        .replaceAll('"', '')
        .replaceAll("'", '')
        .replaceAll('"', '')
        .replaceAll('"', '')
        .replaceAll(''', '')
        .replaceAll(''', '').trim();
  }

  void _loadTodaysShots() {
    final random = math.Random();

    final hindiCount = random.nextBool() ? 3 : 4;
    final englishCount = 7 - hindiCount;

    if (kDebugMode) {
      debugPrint(
        '📊 Today\'s Mix: $hindiCount Hindi + $englishCount English = 7 total',
      );
    }

    List<String> allHindiLines = [];
    List<String> allEnglishLines = [];

    // Collect Hindi lines
    for (String category in hindiCategories) {
      try {
        final categoryLines = PickupLinesData.getLinesForCategory(
          category,
          'Hindi',
        );
        allHindiLines.addAll(categoryLines);
      } catch (e) {
        if (kDebugMode) {
          debugPrint('⚠️ Failed to load Hindi category: $category - $e');
        }
      }
    }

    // Collect English lines
    for (String category in englishCategories) {
      try {
        final categoryLines = PickupLinesData.getLinesForCategory(
          category,
          'English',
        );
        allEnglishLines.addAll(categoryLines);
      } catch (e) {
        if (kDebugMode) {
          debugPrint('⚠️ Failed to load English category: $category - $e');
        }
      }
    }

    // FIXED: Safe selection with proper bounds checking
    List<String> selectedLines = [];
    List<String> selectedLanguages = [];

    if (allHindiLines.isNotEmpty && hindiCount > 0) {
      allHindiLines.shuffle(random);
      final safeHindiCount = math.min(hindiCount, allHindiLines.length);
      final hindiSelection = allHindiLines.take(safeHindiCount);
      selectedLines.addAll(hindiSelection);
      selectedLanguages.addAll(List.filled(hindiSelection.length, 'Hindi'));
    }

    if (allEnglishLines.isNotEmpty && englishCount > 0) {
      allEnglishLines.shuffle(random);
      final safeEnglishCount = math.min(englishCount, allEnglishLines.length);
      final englishSelection = allEnglishLines.take(safeEnglishCount);
      selectedLines.addAll(englishSelection);
      selectedLanguages.addAll(List.filled(englishSelection.length, 'English'));
    }

    if (selectedLines.isNotEmpty) {
      List<MapEntry<String, String>> combinedEntries = [];
      for (int i = 0; i < selectedLines.length; i++) {
        combinedEntries.add(MapEntry(selectedLines[i], selectedLanguages[i]));
      }

      combinedEntries.shuffle(random);

      lines = combinedEntries.map((entry) => _removeQuotes(entry.key)).toList();
      languages = combinedEntries.map((entry) => entry.value).toList();
    }

    if (lines.length < 7) {
      _addFallbackLines(7 - lines.length);
    }

    if (lines.length > 7) {
      lines = lines.take(7).toList();
      languages = languages.take(7).toList();
    }

    if (kDebugMode) {
      debugPrint('🎯 Final Today\'s Shots: ${lines.length} lines loaded');
    }
  }

  void _addFallbackLines(int needed) {
    final fallbackLines = [
      "Are you a magician? Because whenever I look at you, everyone else disappears!",
      "Do you have a map? I keep getting lost in your eyes.",
      "Are you WiFi? Because I'm really feeling a connection.",
      "Is your name Google? Because you have everything I've been searching for.",
      "Are you a parking ticket? Because you've got 'FINE' written all over you.",
      "Do you believe in love at first sight, or should I walk by again?",
      "Are you a bank loan? Because you have my interest!",
    ];

    final random = math.Random();
    for (int i = 0; i < needed && i < fallbackLines.length; i++) {
      final randomIndex = random.nextInt(fallbackLines.length);
      lines.add(fallbackLines[randomIndex]);
      languages.add('English');
    }
  }

  void _clearNotifiers() {
    for (final notifier in cardBackgroundNotifiers.values) {
      notifier.dispose();
    }
    for (final notifier in cardLikedNotifiers.values) {
      notifier.dispose();
    }
    cardBackgroundNotifiers.clear();
    cardLikedNotifiers.clear();
    styledTexts.clear();
  }

  void _changeBackground(int index) {
    if (index >= lines.length || index < 0) return;

    final now = DateTime.now();
    if (_lastTapTime != null &&
        now.difference(_lastTapTime!).inMilliseconds < 100) {
      return;
    }
    _lastTapTime = now;

    if (!cardBackgroundNotifiers.containsKey(index)) {
      cardBackgroundNotifiers[index] = ValueNotifier<int>(0);
    }

    final notifier = cardBackgroundNotifiers[index]!;
    final currentBg = notifier.value;
    final newBg = (currentBg + 1) % BackgroundManager.getTotalBackgroundCount();
    notifier.value = newBg;

    // Haptic feedback after background change for better responsiveness
    HapticFeedback.lightImpact();
  }

  void _updateQuote(int index, String newQuote, [TextStyle? newStyle]) {
    if (!mounted || index >= lines.length || index < 0) return;

    setState(() {
      lines[index] = _removeQuotes(newQuote);
      if (newStyle != null) {
        styledTexts[index] = StyledText.fromTextStyle(
          _removeQuotes(newQuote),
          newStyle,
        );
      }
    });
  }

  void _initializeNotifier(int index) {
    if (index >= lines.length || index < 0) return;

    if (!cardBackgroundNotifiers.containsKey(index)) {
      final randomBgIndex =
          (lines[index].hashCode % BackgroundManager.getTotalBackgroundCount())
              .abs();
      cardBackgroundNotifiers[index] = ValueNotifier<int>(randomBgIndex);
    }

    if (!cardLikedNotifiers.containsKey(index)) {
      cardLikedNotifiers[index] = ValueNotifier<bool>(false);
    }
  }

  void _preloadCriticalImages() {
    if (!mounted) return;

    Future.microtask(() async {
      if (!mounted) return;

      // Use parallel preloading for maximum speed
      final futures = backgroundImages.map(
        (imagePath) =>
            precacheImage(AssetImage(imagePath), context).catchError((error) {
          if (kDebugMode) {
            debugPrint('Failed to preload image: $imagePath');
          }
        }),
      );

      // Wait for all images to preload in parallel
      await Future.wait(futures);
    });
  }

  // FIXED: Create a consistent postId generation method
  String _generatePostId(int index) {
    return 'quote_todays_shots_$index';
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _clearNotifiers();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Consumer2<ThemeProvider, FavoritesProvider>(
      builder: (context, themeProvider, favoritesProvider, child) {
        final cardHeight = _calculateCardHeight(context);

        return Scaffold(
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.grey.shade100,
          appBar: Custom3DAppBar(title: "Today's Shots"),
          body: _buildBody(themeProvider, favoritesProvider, cardHeight),
        );
      },
    );
  }

  double _calculateCardHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = kToolbarHeight;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final availableHeight =
        screenHeight - appBarHeight - statusBarHeight - bottomPadding - 40;

    return ((availableHeight / 1.7) - 20).clamp(300.0, 600.0);
  }

  Widget _buildBody(
    ThemeProvider themeProvider,
    FavoritesProvider favoritesProvider,
    double cardHeight,
  ) {
    return Container(
      color: themeProvider.isDarkMode
          ? Colors.grey.shade900
          : Colors.grey.shade100,
      child: _buildFixedListView(favoritesProvider, cardHeight),
    );
  }

  // FIXED: Complete ListView fix for accessibility errors with proper padding
  Widget _buildFixedListView(
    FavoritesProvider favoritesProvider,
    double cardHeight,
  ) {
    if (lines.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.refresh, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No shots available.\nTap refresh to load new content.',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey, fontSize: 16),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      // FIXED: Add proper padding with top spacing between header and posts
      padding: const EdgeInsets.fromLTRB(
        20,
        20,
        20,
        20,
      ), // Added top padding of 20
      itemCount: lines.length,

      // FIXED: Simplified accessibility approach - remove problematic settings
      cacheExtent: cardHeight * 2.0,
      addAutomaticKeepAlives: true, // CHANGED: Enable keep alives
      addRepaintBoundaries: true,
      // REMOVED: addSemanticIndexes line completely to use default behavior

      // Enhanced smooth scrolling physics
      physics: const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      ),

      scrollDirection: Axis.vertical,
      clipBehavior: Clip.hardEdge,

      // FIXED: Simplified itemBuilder without problematic semantics
      itemBuilder: (context, index) {
        // CRITICAL: Ensure index is always valid
        if (index < 0 || index >= lines.length) {
          return const SizedBox.shrink();
        }

        return Padding(
          padding: const EdgeInsets.only(bottom: 25),
          child: SizedBox(
            height: cardHeight,
            child: _buildOptimizedQuoteCard(
              index,
              favoritesProvider,
              cardHeight,
            ),
          ),
        );
      },
    );
  }

  Widget _buildOptimizedQuoteCard(
    int index,
    FavoritesProvider favoritesProvider,
    double cardHeight,
  ) {
    if (index >= lines.length || index < 0) {
      return const SizedBox.shrink();
    }

    _initializeNotifier(index);

    // FIXED: Use the consistent postId generation method
    final postId = _generatePostId(index);

    if (cardLikedNotifiers.containsKey(index)) {
      cardLikedNotifiers[index]!.value = favoritesProvider.isFavorite(postId);
    }

    final lineLanguage =
        index < languages.length ? languages[index] : 'English';

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return OptimizedMultiPostCard(
          key: ValueKey('todays_shots_card_$index'),
          index: index,
          quote: lines[index],
          styledText: styledTexts[index],
          backgroundImages: backgroundImages,
          backgroundNotifier: cardBackgroundNotifiers[index]!,
          likedNotifier: cardLikedNotifiers[index]!,
          onBackgroundChange: () => _changeBackground(index),
          onQuoteUpdate: (newQuote, newStyle) =>
              _updateQuote(index, newQuote, newStyle),
          themeProvider: themeProvider,
          favoritesProvider: favoritesProvider,
          category: "Today's Shots",
          cardHeight: cardHeight,
          language: lineLanguage,
          // FIXED: Pass the postId to ensure consistency
          postId: postId,
        );
      },
    );
  }
}

// COMPLETE OPTIMIZED CARD COMPONENT
class OptimizedMultiPostCard extends StatefulWidget {
  final int index;
  final String quote;
  final StyledText? styledText;
  final List<String> backgroundImages;
  final ValueNotifier<int> backgroundNotifier;
  final ValueNotifier<bool> likedNotifier;
  final VoidCallback onBackgroundChange;
  final Function(String, TextStyle?) onQuoteUpdate;
  final ThemeProvider themeProvider;
  final FavoritesProvider favoritesProvider;
  final String category;
  final double cardHeight;
  final String language;
  final String postId; // FIXED: Added postId parameter

  const OptimizedMultiPostCard({
    super.key,
    required this.index,
    required this.quote,
    this.styledText,
    required this.backgroundImages,
    required this.backgroundNotifier,
    required this.likedNotifier,
    required this.onBackgroundChange,
    required this.onQuoteUpdate,
    required this.themeProvider,
    required this.favoritesProvider,
    required this.category,
    required this.cardHeight,
    required this.language,
    required this.postId, // FIXED: Added postId parameter
  });

  @override
  State<OptimizedMultiPostCard> createState() => _OptimizedMultiPostCardState();
}

class _OptimizedMultiPostCardState extends State<OptimizedMultiPostCard> {
  final ScreenshotController _screenshotController = ScreenshotController();
  bool _isSaving = false;

  String _cleanQuoteText(String text) {
    return text
        .replaceAll(RegExp(r'^[""]'), '')
        .replaceAll(RegExp(r'[""]$'), '')
        .replaceAll('"', '')
        .replaceAll("'", '')
        .replaceAll('"', '')
        .replaceAll('"', '')
        .replaceAll(''', '')
        .replaceAll(''', '').trim();
  }

  Future<void> _saveAsJPG() async {
    if (_isSaving || !mounted) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final permissionManager = StoragePermissionManager.instance;
      bool hasPermission = await permissionManager.hasStoragePermission();
      if (!hasPermission) {
        hasPermission = await permissionManager.requestStoragePermission();
      }

      if (hasPermission && mounted) {
        _showSavingMessage();

        final Uint8List? pngBytes = await _screenshotController.capture(
          delay: Duration(milliseconds: 50),
          pixelRatio: 2.0,
        );

        if (pngBytes != null && mounted) {
          await _processAndSaveImage(pngBytes);
        }
      } else {
        _showPermissionMessage();
      }
    } catch (e) {
      _showErrorMessage(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _processAndSaveImage(Uint8List pngBytes) async {
    final ui.Codec codec = await ui.instantiateImageCodec(pngBytes);
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    final ui.Image image = frameInfo.image;

    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);
    final Paint paint = Paint()..color = Colors.white;

    canvas.drawRect(
      Rect.fromLTWH(0, 0, image.width.toDouble(), image.height.toDouble()),
      paint,
    );
    canvas.drawImage(image, Offset.zero, Paint());

    final ui.Picture picture = recorder.endRecording();
    final ui.Image finalImage = await picture.toImage(
      image.width,
      image.height,
    );

    final ByteData? jpgByteData = await finalImage.toByteData(
      format: ui.ImageByteFormat.png,
    );

    if (jpgByteData != null) {
      final Uint8List jpgBytes = jpgByteData.buffer.asUint8List();
      final String fileName =
          "charm_shot_todays_shots_${DateTime.now().millisecondsSinceEpoch}";

      await Gal.putImageBytes(jpgBytes, name: fileName);
      _showSuccessMessage();
    }
  }

  void _showSavingMessage() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              SizedBox(width: 16),
              Text('Saving image...'),
            ],
          ),
          backgroundColor: Colors.blue,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _showSuccessMessage() {
    if (mounted) {
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white),
              SizedBox(width: 12),
              Text('Image saved to gallery!'),
            ],
          ),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _showPermissionMessage() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Storage permission required to save images'),
          backgroundColor: Colors.orange,
          action: SnackBarAction(
            label: 'Settings',
            onPressed: () =>
                StoragePermissionManager.instance.openAppSettings(),
          ),
        ),
      );
    }
  }

  void _showErrorMessage(String error) {
    if (mounted) {
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving image: $error'),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    }
    if (kDebugMode) {
      debugPrint('Error saving image: $error');
    }
  }

  // FIXED: Completely rewritten _toggleLike method with proper state management
  void _toggleLike() {
    try {
      // Use the consistent postId passed from parent
      final postId = widget.postId;
      final currentBg =
          widget.backgroundImages[widget.backgroundNotifier.value];

      // Toggle favorite in provider
      widget.favoritesProvider.toggleFavorite(postId, widget.quote, currentBg);

      // Update the notifier with the new status
      bool newStatus = widget.favoritesProvider.isFavorite(postId);
      widget.likedNotifier.value = newStatus;

      // Provide feedback
      HapticFeedback.lightImpact();

      if (widget.favoritesProvider.tapSoundEnabled) {
        SystemSound.play(SystemSoundType.click);
      }

      // Show feedback message
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  newStatus ? Icons.favorite : Icons.favorite_border,
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(width: 12),
                Text(
                  newStatus ? 'Added to favorites!' : 'Removed from favorites',
                ),
              ],
            ),
            backgroundColor: newStatus ? Colors.red : Colors.grey.shade600,
            duration: Duration(seconds: 1),
          ),
        );
      }

      if (kDebugMode) {
        debugPrint(
          '❤️ Like toggled for postId: $postId, newStatus: $newStatus',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error toggling like: $e');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating favorite: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _copyToClipboard() async {
    try {
      await Clipboard.setData(
        ClipboardData(text: _cleanQuoteText(widget.quote)),
      );

      if (widget.favoritesProvider.tapSoundEnabled) {
        SystemSound.play(SystemSoundType.click);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check, color: Colors.white),
                SizedBox(width: 12),
                Text('Quote copied to clipboard!'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to copy: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _shareQuote() async {
    try {
      await SharePlus.instance.share(
        ShareParams(
          text:
              '${_cleanQuoteText(widget.quote)}\n\n💕 Shared from Charm Shots - The ultimate pickup lines app!',
          subject: 'Check out this pickup line from Today\'s Shots!',
        ),
      );

      if (widget.favoritesProvider.tapSoundEnabled) {
        SystemSound.play(SystemSoundType.click);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: widget.backgroundNotifier,
      builder: (context, currentBackgroundIndex, child) {
        return ValueListenableBuilder<bool>(
          valueListenable: widget.likedNotifier,
          builder: (context, isLiked, child) {
            return Container(
              height: widget.cardHeight,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    spreadRadius: 0,
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  SizedBox(
                    height: widget.cardHeight - 80,
                    child: _buildContentArea(currentBackgroundIndex),
                  ),
                  SizedBox(height: 80, child: _buildActionArea(isLiked)),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildContentArea(int currentBackgroundIndex) {
    final cleanedText = _cleanQuoteText(widget.quote);

    return GestureDetector(
      onTap: widget.onBackgroundChange,
      child: Stack(
        children: [
          Screenshot(
            controller: _screenshotController,
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              child: FastBackgroundWidget(
                backgroundIndex: currentBackgroundIndex,
                colorFilter: ColorFilter.mode(
                  Colors.black.withValues(alpha: 0.3),
                  BlendMode.darken,
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                child: Column(
                children: [
                  // Language indicator
                  Padding(
                    padding: const EdgeInsets.only(top: 16, left: 16),
                    child: Align(
                      alignment: Alignment.topLeft,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: widget.language == 'Hindi'
                              ? Colors.orange.withValues(alpha: 0.8)
                              : Colors.blue.withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              widget.language == 'Hindi' ? '🇮🇳' : '🇺🇸',
                              style: TextStyle(fontSize: 12),
                            ),
                            SizedBox(width: 4),
                            Text(
                              widget.language,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 28,
                          vertical: 20,
                        ),
                        child: Text(
                          cleanedText,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 24,
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            height: 1.4,
                            letterSpacing: 0.3,
                            shadows: [
                              Shadow(
                                offset: Offset(0, 1),
                                blurRadius: 2,
                                color: Colors.black54,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16, right: 16),
                    child: Align(
                      alignment: Alignment.bottomRight,
                      child: Text(
                        '@Charm Shots',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white38,
                          fontWeight: FontWeight.w400,
                          letterSpacing: 0.3,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Positioned(top: 16, right: 16, child: _buildEditButton()),
        ],
      ),
    );
  }

  Widget _buildEditButton() {
    return GestureDetector(
      onTap: () {
        showEnhancedEditDialog(
          context: context,
          initialText: widget.quote,
          onSave: (newText, newStyle) {
            widget.onQuoteUpdate(newText, newStyle);
          },
        );
      },
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.95),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              spreadRadius: 0,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(Icons.edit, color: Colors.black87, size: 20),
      ),
    );
  }

  Widget _buildActionArea(bool isLiked) {
    return Container(
      width: double.infinity,
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05),
            spreadRadius: 0,
            blurRadius: 2,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionButton(
              icon: isLiked ? Icons.favorite : Icons.favorite_border,
              label: 'Like',
              isActive: isLiked,
              onTap: _toggleLike,
            ),
            _buildActionButton(
              icon: _isSaving ? Icons.hourglass_empty : Icons.download,
              label: 'Save',
              onTap: _isSaving ? null : _saveAsJPG,
            ),
            _buildActionButton(
              icon: Icons.copy,
              label: 'Copy',
              onTap: _copyToClipboard,
            ),
            _buildActionButton(
              icon: Icons.share,
              label: 'Share',
              onTap: _shareQuote,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback? onTap,
    bool isActive = false,
  }) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          splashColor: isActive && label == 'Like'
              ? Colors.red.withValues(alpha: 0.2)
              : Colors.grey.withValues(alpha: 0.1),
          child: Container(
            height: 64,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color:
                      isActive && label == 'Like' ? Colors.red : Colors.black87,
                  size: 20,
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: TextStyle(
                    color: isActive && label == 'Like'
                        ? Colors.red
                        : Colors.black87,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
