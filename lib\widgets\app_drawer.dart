// lib/widgets/app_drawer.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../screens/performance_debug_screen.dart';
import '../screens/todays_shots_screen.dart';
import '../screens/shots_maker_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/favorites_screen.dart';
import '../screens/privacy_policy_screen.dart';
import '../screens/terms_conditions_screen.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';
import '../utils/animation_utils.dart';
import 'smooth_drawer.dart';

/// Navigation state management for drawer
class DrawerNavigationState {
  static bool _shouldOpenDrawerOnReturn = false;
  static DateTime? _drawerNavigationTime;

  static bool get shouldOpenDrawerOnReturn {
    if (_shouldOpenDrawerOnReturn && _drawerNavigationTime != null) {
      final timeDiff = DateTime.now().difference(_drawerNavigationTime!);
      return timeDiff.inSeconds < 3;
    }
    return false;
  }

  static void setNavigationFlag() {
    _shouldOpenDrawerOnReturn = true;
    _drawerNavigationTime = DateTime.now();
  }

  static void resetNavigationFlag() {
    _shouldOpenDrawerOnReturn = false;
    _drawerNavigationTime = null;
  }
}

/// Drawer menu item data model
class DrawerMenuItem {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback onTap;
  final Color? iconColor;
  final bool isDividerAfter;
  final bool isDebugOnly;

  const DrawerMenuItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.subtitle,
    this.iconColor,
    this.isDividerAfter = false,
    this.isDebugOnly = false,
  });
}

class AppDrawer extends StatefulWidget {
  const AppDrawer({super.key});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  String _version = "1.0.0";

  @override
  void initState() {
    super.initState();
    _getVersionInfo();
  }

  Future<void> _getVersionInfo() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      if (mounted) {
        setState(() {
          _version = "v${packageInfo.version}";
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _version = "v1.0.0";
        });
      }
    }
  }

  Future<void> _shareApp() async {
    try {
      const String appName = "Charm Shots";
      const String shareText = """
🌟 Check out $appName! 🌟

The ultimate pickup lines app with amazing features:
✨ Daily pickup lines
💝 Save your favorites
🎨 Create custom shots
🌙 Dark mode support

Download now and charm your way to success!

#CharmShots #PickupLines #Dating #Romance
""";

      await SharePlus.instance.share(
        ShareParams(text: shareText, subject: 'Check out $appName!'),
      );
    } catch (e) {
      await SharePlus.instance.share(
        ShareParams(
          text: "Check out Charm Shots - The ultimate pickup lines app! 🌟",
          subject: 'Check out Charm Shots!',
        ),
      );
    }
  }

  Future<void> _rateApp() async {
    const String playStoreUrl =
        "https://play.google.com/store/apps/details?id=com.example.charmshots.charmshot1";

    try {
      final Uri url = Uri.parse(playStoreUrl);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          _showRateAppDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        _showRateAppDialog();
      }
    }
  }

  void _showRateAppDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Rate Charm Shots'),
          content: const Text(
            'We would love your feedback! Please rate us on the Google Play Store.\n\n'
            'Search for "Charm Shots" in the Play Store or visit our app page.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _navigateTo(Widget screen) {
    // Add haptic feedback
    HapticFeedback.lightImpact();

    Navigator.pop(context);
    if (mounted) {
      // Set flag to open drawer when returning to language selection
      DrawerNavigationState.setNavigationFlag();
      Navigator.push(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => screen,
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeInOutCubic,
              )),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 300),
          settings: const RouteSettings(arguments: {'fromDrawer': true}),
        ),
      );
    }
  }

  Future<void> _handleExternalAction(VoidCallback action) async {
    HapticFeedback.selectionClick();
    Navigator.pop(context);
    await Future.delayed(const Duration(milliseconds: 200));
    action();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return ModernDrawer(
          backgroundColor:
              themeProvider.isDarkMode ? Colors.grey.shade900 : Colors.white,
          width: MediaQuery.of(context).size.width * 0.85,
          child: SafeArea(
            child: Column(
              children: [
                _buildModernHeader(themeProvider),
                Expanded(
                  child: _buildModernMenuItems(themeProvider),
                ),
                _buildFooter(themeProvider),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildModernHeader(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 32, 24, 24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: themeProvider.isDarkMode
              ? [
                  Colors.grey.shade900,
                  Colors.grey.shade800,
                ]
              : [
                  Colors.white,
                  Colors.grey.shade50,
                ],
        ),
      ),
      child: Column(
        children: [
          // App logo with subtle animation
          AnimatedDrawerItem(
            index: 0,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF667eea),
                    const Color(0xFF764ba2),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF667eea).withValues(alpha: 0.3),
                    blurRadius: 20,
                    spreadRadius: 0,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Image.asset(
                'assets/images/charm_logo.png',
                width: 40,
                height: 40,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // App title
          AnimatedDrawerItem(
            index: 1,
            child: Text(
              'Charm Shots',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
                letterSpacing: 0.5,
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Subtitle
          AnimatedDrawerItem(
            index: 2,
            child: Text(
              'Perfect Lines, Perfect Moments',
              style: TextStyle(
                fontSize: 14,
                color: themeProvider.isDarkMode
                    ? Colors.grey.shade400
                    : Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          const SizedBox(height: 24),
          _buildModernSeparator(themeProvider),
        ],
      ),
    );
  }

  Widget _buildModernSeparator(ThemeProvider themeProvider) {
    return Container(
      height: 2,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(1),
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            themeProvider.isDarkMode
                ? Colors.white.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.3),
            Colors.transparent,
          ],
        ),
      ),
    );
  }

  Widget _buildModernMenuItems(ThemeProvider themeProvider) {
    final menuItems = _getMenuItems(themeProvider);

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      physics: const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      ),
      itemCount: menuItems.length,
      itemBuilder: (context, index) {
        final item = menuItems[index];

        if (item.isDebugOnly && !kDebugMode) {
          return const SizedBox.shrink();
        }

        return Column(
          children: [
            AnimatedDrawerItem(
              index: index + 3, // Start after header items
              child: _buildModernMenuItem(item, themeProvider),
            ),
            if (item.isDividerAfter) ...[
              const SizedBox(height: 16),
              _buildModernSeparator(themeProvider),
              const SizedBox(height: 8),
            ],
          ],
        );
      },
    );
  }

  List<DrawerMenuItem> _getMenuItems(ThemeProvider themeProvider) {
    return [
      DrawerMenuItem(
        icon: Icons.today_rounded,
        title: "Today's Shots",
        onTap: () => _navigateTo(const TodaysShotsScreen()),
      ),
      DrawerMenuItem(
        icon: Icons.favorite_rounded,
        title: "Favourites",
        subtitle: "${context.read<FavoritesProvider>().favoritesCount} saved",
        onTap: () => _navigateTo(FavoritesScreen()),
      ),
      DrawerMenuItem(
        icon: Icons.edit_rounded,
        title: "Shots Maker",
        subtitle: "Create custom shots",
        onTap: () => _navigateTo(ShotsMakerScreen()),
      ),
      DrawerMenuItem(
        icon: Icons.settings_rounded,
        title: "Settings",
        onTap: () => _navigateTo(SettingsScreen()),
        isDividerAfter: true,
      ),
      DrawerMenuItem(
        icon: Icons.privacy_tip_rounded,
        title: "Privacy Policy",
        onTap: () => _navigateTo(const PrivacyPolicyScreen()),
      ),
      DrawerMenuItem(
        icon: Icons.description_rounded,
        title: "Terms & Conditions",
        onTap: () => _navigateTo(const TermsConditionsScreen()),
      ),
      DrawerMenuItem(
        icon: Icons.star_rate_rounded,
        title: "Rate Us",
        subtitle: "Love the app? Rate us!",
        onTap: () => _handleExternalAction(_rateApp),
        iconColor: Colors.amber,
      ),
      DrawerMenuItem(
        icon: Icons.share_rounded,
        title: "Share App",
        subtitle: "Tell your friends",
        onTap: () => _handleExternalAction(_shareApp),
        iconColor: Colors.blue,
      ),
      if (kDebugMode)
        DrawerMenuItem(
          icon: Icons.bug_report_rounded,
          title: "Performance Debug",
          subtitle: "Debug performance issues",
          onTap: () => _navigateTo(PerformanceDebugScreen()),
          iconColor: Colors.orange,
          isDebugOnly: true,
        ),
    ];
  }

  Widget _buildModernMenuItem(
      DrawerMenuItem item, ThemeProvider themeProvider) {
    return TapAnimationWrapper(
      onTap: item.onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.transparent,
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          child: InkWell(
            onTap: item.onTap,
            borderRadius: BorderRadius.circular(16),
            splashColor: themeProvider.isDarkMode
                ? Colors.white.withValues(alpha: 0.1)
                : Colors.grey.withValues(alpha: 0.1),
            highlightColor: themeProvider.isDarkMode
                ? Colors.white.withValues(alpha: 0.05)
                : Colors.grey.withValues(alpha: 0.05),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  // Icon with background
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: (item.iconColor ??
                              (themeProvider.isDarkMode
                                  ? Colors.white
                                  : Colors.black87))
                          .withValues(alpha: 0.1),
                    ),
                    child: Icon(
                      item.icon,
                      color: item.iconColor ??
                          (themeProvider.isDarkMode
                              ? Colors.white
                              : Colors.black87),
                      size: 22,
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Title and subtitle
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.title,
                          style: TextStyle(
                            color: themeProvider.isDarkMode
                                ? Colors.white
                                : Colors.black87,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.2,
                          ),
                        ),
                        if (item.subtitle != null) ...[
                          const SizedBox(height: 2),
                          Text(
                            item.subtitle!,
                            style: TextStyle(
                              color: themeProvider.isDarkMode
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Arrow icon
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    color: themeProvider.isDarkMode
                        ? Colors.grey.shade500
                        : Colors.grey.shade400,
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFooter(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          _buildModernSeparator(themeProvider),
          const SizedBox(height: 16),
          AnimatedDrawerItem(
            index: 20, // High index for last animation
            child: Text(
              _version,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: themeProvider.isDarkMode
                    ? Colors.grey.shade500
                    : Colors.grey.shade500,
                fontSize: 12,
                fontWeight: FontWeight.w500,
                letterSpacing: 0.5,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
