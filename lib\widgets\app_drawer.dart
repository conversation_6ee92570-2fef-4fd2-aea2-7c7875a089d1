// lib/widgets/app_drawer.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../screens/performance_debug_screen.dart';
import '../screens/todays_shots_screen.dart';
import '../screens/shots_maker_screen.dart';
import '../screens/settings_screen.dart';
import '../screens/favorites_screen.dart';
import '../screens/privacy_policy_screen.dart';
import '../screens/terms_conditions_screen.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';
import 'smooth_drawer.dart';

// Global flag to track drawer navigation with timestamp
bool _shouldOpenDrawerOnReturn = false;
DateTime? _drawerNavigationTime;

// Getter and setter for the drawer flag
bool get shouldOpenDrawerOnReturn {
  // Only return true if less than 3 seconds have passed
  if (_shouldOpenDrawerOnReturn && _drawerNavigationTime != null) {
    final timeDiff = DateTime.now().difference(_drawerNavigationTime!);
    return timeDiff.inSeconds < 3;
  }
  return false;
}

void resetDrawerFlag() {
  _shouldOpenDrawerOnReturn = false;
  _drawerNavigationTime = null;
}

class AppDrawer extends StatefulWidget {
  const AppDrawer({super.key});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  String _version = "1.0.0";

  @override
  void initState() {
    super.initState();
    _getVersionInfo();
  }

  Future<void> _getVersionInfo() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      if (mounted) {
        setState(() {
          _version = "v${packageInfo.version}";
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _version = "v1.0.0";
        });
      }
    }
  }

  Future<void> _shareApp() async {
    try {
      const String appName = "Charm Shots";
      const String shareText = """
🌟 Check out $appName! 🌟

The ultimate pickup lines app with amazing features:
✨ Daily pickup lines
💝 Save your favorites
🎨 Create custom shots
🌙 Dark mode support

Download now and charm your way to success!

#CharmShots #PickupLines #Dating #Romance
""";

      await SharePlus.instance.share(
        ShareParams(text: shareText, subject: 'Check out $appName!'),
      );
    } catch (e) {
      await SharePlus.instance.share(
        ShareParams(
          text: "Check out Charm Shots - The ultimate pickup lines app! 🌟",
          subject: 'Check out Charm Shots!',
        ),
      );
    }
  }

  Future<void> _rateApp() async {
    const String playStoreUrl =
        "https://play.google.com/store/apps/details?id=com.example.charmshots.charmshot1";

    try {
      final Uri url = Uri.parse(playStoreUrl);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          _showRateAppDialog();
        }
      }
    } catch (e) {
      if (mounted) {
        _showRateAppDialog();
      }
    }
  }

  void _showRateAppDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Rate Charm Shots'),
          content: const Text(
            'We would love your feedback! Please rate us on the Google Play Store.\n\n'
            'Search for "Charm Shots" in the Play Store or visit our app page.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _navigateTo(Widget screen) {
    Navigator.pop(context);
    if (mounted) {
      // Set flag to open drawer when returning to language selection
      _shouldOpenDrawerOnReturn = true;
      _drawerNavigationTime = DateTime.now();
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => screen,
          settings: const RouteSettings(arguments: {'fromDrawer': true}),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return SmoothDrawer(
          backgroundColor:
              themeProvider.isDarkMode ? Colors.grey.shade800 : Colors.white,
          elevation: 16,
          width: MediaQuery.of(context).size.width * 0.85,
          child: Column(
            children: [
              _buildHeader(themeProvider),
              Expanded(
                child: _buildMenuItems(themeProvider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.only(top: 40, bottom: 16),
      decoration: BoxDecoration(
        color: themeProvider.isDarkMode ? Colors.grey.shade800 : Colors.white,
        boxShadow: [
          BoxShadow(
            color: themeProvider.isDarkMode
                ? Colors.black.withValues(alpha: 0.4)
                : Colors.black.withValues(alpha: 0.12),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        children: [
          Center(
            child: Column(
              children: [
                Image.asset('assets/images/charm_logo.png', width: 60),
                const SizedBox(height: 12),
                Text(
                  'Charm Shots',
                  style: TextStyle(
                    fontSize: 22,
                    color:
                        themeProvider.isDarkMode ? Colors.white : Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          _buildSeparator(),
        ],
      ),
    );
  }

  Widget _buildSeparator() {
    return Container(
      height: 1,
      margin: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.transparent,
            Colors.grey.withValues(alpha: 0.3),
            Colors.transparent,
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItems(ThemeProvider themeProvider) {
    return ListView(
      padding: EdgeInsets.zero,
      // Enhanced smooth scrolling for drawer
      physics: const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      ),
      children: [
        const SizedBox(height: 8),
        SmoothDrawerItem(
          index: 0,
          child: _buildMenuItem(
            Icons.today,
            "Today's Shots",
            () => _navigateTo(const TodaysShotsScreen()),
            themeProvider,
          ),
        ),
        Consumer<FavoritesProvider>(
          builder: (context, favoritesProvider, child) {
            return SmoothDrawerItem(
              index: 1,
              child: _buildMenuItem(
                Icons.favorite,
                "Favourite (${favoritesProvider.favoritesCount})",
                () => _navigateTo(FavoritesScreen()),
                themeProvider,
              ),
            );
          },
        ),
        SmoothDrawerItem(
          index: 2,
          child: _buildMenuItem(
            Icons.edit,
            "Shots Maker",
            () => _navigateTo(ShotsMakerScreen()),
            themeProvider,
          ),
        ),
        SmoothDrawerItem(
          index: 3,
          child: _buildMenuItem(
            Icons.settings,
            "Settings",
            () => _navigateTo(SettingsScreen()),
            themeProvider,
          ),
        ),
        const SizedBox(height: 16),
        SmoothDrawerItem(
          index: 4,
          child: _buildMenuItem(
            Icons.privacy_tip,
            "Privacy Policy",
            () => _navigateTo(const PrivacyPolicyScreen()),
            themeProvider,
          ),
        ),
        SmoothDrawerItem(
          index: 5,
          child: _buildMenuItem(
            Icons.description,
            "Terms and Conditions",
            () => _navigateTo(const TermsConditionsScreen()),
            themeProvider,
          ),
        ),
        SmoothDrawerItem(
          index: 6,
          child: _buildMenuItem(
            Icons.star_rate,
            "Rate Us",
            () {
              Navigator.pop(context);
              _rateApp();
            },
            themeProvider,
          ),
        ),
        SmoothDrawerItem(
          index: 7,
          child: _buildMenuItem(
            Icons.share,
            "Share App",
            () {
              Navigator.pop(context);
              _shareApp();
            },
            themeProvider,
          ),
        ),
        if (kDebugMode) ...[
          const SizedBox(height: 16),
          _buildSeparator(),
          const SizedBox(height: 8),
          SmoothDrawerItem(
            index: 8,
            child: _buildMenuItem(
              Icons.bug_report,
              "Performance Debug",
              () => _navigateTo(PerformanceDebugScreen()),
              themeProvider,
              subtitle: "Debug performance issues",
              iconColor: Colors.orange,
            ),
          ),
        ],
        const SizedBox(height: 40),
        Container(
          padding: const EdgeInsets.all(16),
          child: Text(
            _version,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: themeProvider.isDarkMode
                  ? Colors.grey.shade400
                  : Colors.grey.shade600,
              fontSize: 12,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem(
    IconData icon,
    String title,
    VoidCallback onTap,
    ThemeProvider themeProvider, {
    String? subtitle,
    Color? iconColor,
  }) {
    return SmoothTapAnimation(
      onTap: onTap,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          splashColor: themeProvider.isDarkMode
              ? Colors.grey.shade700.withValues(alpha: 0.3)
              : Colors.grey.shade200,
          highlightColor: themeProvider.isDarkMode
              ? Colors.grey.shade800.withValues(alpha: 0.2)
              : Colors.grey.shade100,
          child: ListTile(
            leading: Icon(
              icon,
              color: iconColor ??
                  (themeProvider.isDarkMode ? Colors.white70 : Colors.black87),
              size: 24,
            ),
            title: Text(
              title,
              style: TextStyle(
                color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            subtitle: subtitle != null
                ? Text(
                    subtitle,
                    style: TextStyle(
                      color: themeProvider.isDarkMode
                          ? Colors.grey.shade400
                          : Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  )
                : null,
          ),
        ),
      ),
    );
  }
}
