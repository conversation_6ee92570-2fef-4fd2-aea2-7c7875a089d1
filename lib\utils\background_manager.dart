// lib/utils/background_manager.dart
// Centralized background management for pickup lines app
// Supports random backgrounds and easy scaling to more images

import 'dart:math';
import 'package:flutter/material.dart';

class BackgroundManager {
  static final BackgroundManager _instance = BackgroundManager._internal();
  factory BackgroundManager() => _instance;
  BackgroundManager._internal();

  static final Random _random = Random();

  // Available backgrounds with their actual file extensions
  static const Map<int, String> _availableBackgrounds = {
    0: 'png',
    1: 'png',
    2: 'png',
    3: 'png',
    4: 'png',
    5: 'png',
    6: 'png',
    7: 'png', // Using .png version (both .jpg and .png exist, prefer .png)
    8: 'png',
    9: 'png',
    10: 'png',
  };

  // Fast-loading gradient fallbacks for each background
  static const List<List<Color>> _gradientFallbacks = [
    [Color(0xFF667eea), Color(0xFF764ba2)], // 0 - Purple Blue
    [Color(0xFF43cea2), Color(0xFF185a9d)], // 1 - Green Blue
    [Color(0xFFf093fb), Color(0xFFf5576c)], // 2 - Pink Red
    [Color(0xFF4facfe), Color(0xFF00f2fe)], // 3 - Blue Cyan
    [Color(0xFFa8edea), Color(0xFFfed6e3)], // 4 - Mint Pink
    [Color(0xFF8360c3), Color(0xFF2ebf91)], // 5 - Purple Green
    [Color(0xFFffecd2), Color(0xFFfcb69f)], // 6 - Orange Peach
    [Color(0xFF667eea), Color(0xFF764ba2)], // 7 - Purple Blue
    [Color(0xFF89f7fe), Color(0xFF66a6ff)], // 8 - Light Blue
    [Color(0xFFfbc2eb), Color(0xFFa6c1ee)], // 9 - Pink Blue
    [Color(0xFFffecd2), Color(0xFFfcb69f)], // 10 - Orange Peach
  ];

  /// Get all available background image paths
  static List<String> getAllBackgrounds() {
    return _availableBackgrounds.entries
        .map((entry) => 'assets/images/backgrounds/${entry.key}.${entry.value}')
        .toList();
  }

  /// Get a random background image path
  static String getRandomBackground() {
    final randomIndex = _random.nextInt(_availableBackgrounds.length);
    final entry = _availableBackgrounds.entries.elementAt(randomIndex);
    return 'assets/images/backgrounds/${entry.key}.${entry.value}';
  }

  /// Get a random background different from the current one
  static String getRandomBackgroundExcluding(String currentBackground) {
    if (_availableBackgrounds.length <= 1) {
      return getAllBackgrounds().first;
    }

    String newBackground;
    do {
      newBackground = getRandomBackground();
    } while (newBackground == currentBackground);

    return newBackground;
  }

  /// Get background by index (for cycling through backgrounds)
  static String getBackgroundByIndex(int index) {
    final safeIndex = index % _availableBackgrounds.length;
    final entry = _availableBackgrounds.entries.elementAt(safeIndex);
    return 'assets/images/backgrounds/${entry.key}.${entry.value}';
  }

  /// Get next background in sequence
  static String getNextBackground(String currentBackground) {
    final allBackgrounds = getAllBackgrounds();
    final currentIndex = allBackgrounds.indexOf(currentBackground);

    if (currentIndex == -1) {
      return allBackgrounds.first;
    }

    final nextIndex = (currentIndex + 1) % allBackgrounds.length;
    return allBackgrounds[nextIndex];
  }

  /// Get total number of available backgrounds
  static int getTotalBackgroundCount() {
    return _availableBackgrounds.length;
  }

  /// Check if a background exists
  static bool backgroundExists(String backgroundPath) {
    return getAllBackgrounds().contains(backgroundPath);
  }

  /// Get random backgrounds for multiple posts (ensures variety)
  static List<String> getRandomBackgroundsForPosts(int postCount) {
    final List<String> backgrounds = [];
    final allBackgrounds = getAllBackgrounds();

    for (int i = 0; i < postCount; i++) {
      // Try to avoid consecutive duplicates
      String newBackground;
      do {
        newBackground = getRandomBackground();
      } while (backgrounds.isNotEmpty &&
          backgrounds.last == newBackground &&
          allBackgrounds.length > 1);

      backgrounds.add(newBackground);
    }

    return backgrounds;
  }

  /// Get a seeded random background (consistent for same seed)
  static String getSeededRandomBackground(int seed) {
    final seededRandom = Random(seed);
    final randomIndex = seededRandom.nextInt(_availableBackgrounds.length);
    final entry = _availableBackgrounds.entries.elementAt(randomIndex);
    return 'assets/images/backgrounds/${entry.key}.${entry.value}';
  }

  /// Get background based on post content hash (consistent per post)
  static String getBackgroundForPost(String postContent) {
    final hash = postContent.hashCode.abs();
    final index = hash % _availableBackgrounds.length;
    final entry = _availableBackgrounds.entries.elementAt(index);
    return 'assets/images/backgrounds/${entry.key}.${entry.value}';
  }

  /// Future expansion helper - add new backgrounds easily
  /// When you add backgrounds 11-20, just call this method
  static void addNewBackgrounds(List<int> newBackgroundNumbers) {
    // This is a placeholder for future expansion
    // In the future, you can make _availableBackgrounds non-const
    // and add new backgrounds dynamically

    // Example usage when you add 11.png to 20.png:
    // BackgroundManager.addNewBackgrounds([11, 12, 13, 14, 15, 16, 17, 18, 19, 20]);
  }

  /// Get gradient fallback for a background index
  static List<Color> getGradientFallback(int index) {
    final safeIndex = index % _gradientFallbacks.length;
    return _gradientFallbacks[safeIndex];
  }

  /// Get gradient fallback for a background path
  static List<Color> getGradientFallbackForPath(String backgroundPath) {
    // Extract index from path like "assets/images/backgrounds/5.png"
    final regex = RegExp(r'(\d+)\.png$');
    final match = regex.firstMatch(backgroundPath);
    if (match != null) {
      final index = int.tryParse(match.group(1) ?? '0') ?? 0;
      return getGradientFallback(index);
    }
    return _gradientFallbacks[0]; // Default fallback
  }

  /// Check if background asset exists by index (for error handling)
  static bool backgroundExistsByIndex(int index) {
    return _availableBackgrounds.containsKey(index);
  }

  /// Get safe background path (returns first available if index doesn't exist)
  static String getSafeBackgroundPath(int index) {
    if (backgroundExistsByIndex(index)) {
      final entry = _availableBackgrounds.entries.elementAt(index);
      return 'assets/images/backgrounds/${entry.key}.${entry.value}';
    }
    // Return first available background as fallback
    final firstEntry = _availableBackgrounds.entries.first;
    return 'assets/images/backgrounds/${firstEntry.key}.${firstEntry.value}';
  }

  /// Get background info for debugging
  static Map<String, dynamic> getBackgroundInfo() {
    return {
      'totalBackgrounds': getTotalBackgroundCount(),
      'availableNumbers': _availableBackgrounds,
      'allPaths': getAllBackgrounds(),
      'gradientFallbacks': _gradientFallbacks.length,
    };
  }
}
