// lib/screens/performance_debug_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/performance_utils.dart';
import '../utils/image_cache_manager.dart';
import '../widgets/custom_app_bar.dart';

class PerformanceDebugScreen extends StatefulWidget {
  const PerformanceDebugScreen({super.key});

  @override
  State<PerformanceDebugScreen> createState() => _PerformanceDebugScreenState();
}

class _PerformanceDebugScreenState extends State<PerformanceDebugScreen> {
  String _performanceReport = '';
  bool _isMonitoring = false;

  @override
  void initState() {
    super.initState();
    _updateReport();
  }

  void _updateReport() {
    setState(() {
      _performanceReport = PerformanceUtils.generatePerformanceReport();
    });
  }

  void _toggleMonitoring() {
    setState(() {
      if (_isMonitoring) {
        PerformanceUtils.stopFrameMonitoring();
        _isMonitoring = false;
      } else {
        PerformanceUtils.startFrameMonitoring();
        _isMonitoring = true;
      }
    });
  }

  void _resetCounters() {
    PerformanceUtils.resetCounters();
    _updateReport();
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Performance counters reset')));
  }

  void _clearImageCache() {
    OptimizedImageCache().clearCache();
    _updateReport();
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Image cache cleared')));
  }

  void _copyReportToClipboard() {
    Clipboard.setData(ClipboardData(text: _performanceReport));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Performance report copied to clipboard')),
    );
  }

  void _runPerformanceTests() async {
    if (!mounted) return;

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Running performance tests...')));

    // Run basic performance checks
    await _runBasicPerformanceTests();

    if (!mounted) return;
    _updateReport();

    if (!mounted) return;
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Performance tests completed!')));
  }

  Future<void> _runBasicPerformanceTests() async {
    // Clear and test image cache
    OptimizedImageCache().clearCache();

    // Test memory usage
    PerformanceUtils.logMemoryUsage('Performance Test');

    // Simulate some work and track widget rebuilds
    await Future.delayed(Duration(milliseconds: 500));

    // Track performance
    PerformanceUtils.trackWidgetRebuild('PerformanceDebugScreen');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: Custom3DAppBar(
        title: 'Performance Debug',
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _updateReport,
            tooltip: 'Refresh Report',
          ),
        ],
      ),
      backgroundColor: Colors.white,
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Control buttons
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _toggleMonitoring,
                  icon: Icon(_isMonitoring ? Icons.stop : Icons.play_arrow),
                  label: Text(
                    _isMonitoring ? 'Stop Monitoring' : 'Start Monitoring',
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isMonitoring ? Colors.red : Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _resetCounters,
                  icon: Icon(Icons.refresh),
                  label: Text('Reset Counters'),
                ),
                ElevatedButton.icon(
                  onPressed: _clearImageCache,
                  icon: Icon(Icons.clear_all),
                  label: Text('Clear Cache'),
                ),
                ElevatedButton.icon(
                  onPressed: _copyReportToClipboard,
                  icon: Icon(Icons.copy),
                  label: Text('Copy Report'),
                ),
                ElevatedButton.icon(
                  onPressed: _runPerformanceTests,
                  icon: Icon(Icons.science),
                  label: Text('Run Tests'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            SizedBox(height: 20),

            // Performance tips
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.lightbulb, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'Performance Tips',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• Frame drops > 5%: Consider reducing animations or image sizes\n'
                    '• High widget rebuilds: Check for unnecessary setState calls\n'
                    '• Large image cache: Clear cache periodically or reduce image quality\n'
                    '• Use RepaintBoundary for complex widgets that don\'t change often',
                    style: TextStyle(fontSize: 12, color: Colors.blue.shade700),
                  ),
                ],
              ),
            ),

            SizedBox(height: 20),

            // Performance report
            Text(
              'Performance Report',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),

            Expanded(
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _performanceReport.isEmpty
                        ? 'No performance data available. Start monitoring to collect data.'
                        : _performanceReport,
                    style: TextStyle(fontFamily: 'monospace', fontSize: 12),
                  ),
                ),
              ),
            ),

            SizedBox(height: 10),

            // Auto-refresh toggle
            Row(
              children: [
                Text('Auto-refresh every 2 seconds'),
                Spacer(),
                StreamBuilder<void>(
                  stream: Stream.periodic(Duration(seconds: 2)),
                  builder: (context, snapshot) {
                    if (_isMonitoring) {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        _updateReport();
                      });
                    }
                    return SizedBox.shrink();
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
